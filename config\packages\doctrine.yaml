doctrine:
    dbal:
        default_connection: default
        connections:
            default:
                url: '%env(resolve:DATABASE_URL)%'
                #server_version: '16'
                profiling_collect_backtrace: '%kernel.debug%'
                use_savepoints: true

            legacy:
                driver: 'pdo_mysql'
                host:   '%env(resolve:LEGACY_DB_HOST)%'
                port:   '%env(int:LEGACY_DB_PORT)%'
                dbname: '%env(resolve:LEGACY_DB_NAME)%'
                user:   '%env(resolve:LEGACY_DB_USER)%'
                password: '%env(resolve:LEGACY_DB_PASSWORD)%'
                charset: utf8mb4

    orm:
        auto_generate_proxy_classes: '%kernel.debug%'
        enable_lazy_ghost_objects: true
        report_fields_where_declared: true
        validate_xml_mapping: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        identity_generation_preferences:
            Doctrine\DBAL\Platforms\PostgreSQLPlatform: identity
        auto_mapping: true
        # Configuration du cache de métadonnées
        metadata_cache_driver:
            type: pool
            pool: doctrine.system_cache_pool
        # Configuration du cache de requêtes
        query_cache_driver:
            type: pool
            pool: doctrine.system_cache_pool
        # Configuration du cache de résultats
        result_cache_driver:
            type: pool
            pool: doctrine.result_cache_pool
        mappings:
            App:
                type: attribute
                is_bundle: false
                dir: '%kernel.project_dir%/src/Entity'
                prefix: 'App\Entity'
                alias: App
        controller_resolver:
            auto_mapping: false
        dql:
            numeric_functions:
                YEAR: DoctrineExtensions\Query\Mysql\Year
                MONTH: DoctrineExtensions\Query\Mysql\Month
            string_functions:
                DATE_FORMAT: DoctrineExtensions\Query\Mysql\DateFormat

when@test:
    doctrine:
        dbal:
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
