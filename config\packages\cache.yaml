framework:
    cache:
        # Unique name of your app: used to compute stable namespaces for cache keys.
        prefix_seed: 'frcmintranet_app'

        # Configuration optimisée pour la production sans APCu
        # Utilise le cache filesystem avec optimisations
        app: cache.adapter.filesystem

        # Configuration des pools de cache spécialisés
        pools:
            # Cache pour les données de documents (courte durée)
            document.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 300 # 5 minutes

            # Cache pour les statistiques navbar (très courte durée)
            navbar.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 120 # 2 minutes

            # Cache pour les données de workflow (durée moyenne)
            workflow.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 1800 # 30 minutes

            # Cache système Doctrine
            doctrine.system_cache_pool:
                adapter: cache.adapter.filesystem
                default_lifetime: 3600 # 1 heure

            # Cache résultats Doctrine
            doctrine.result_cache_pool:
                adapter: cache.adapter.filesystem
                default_lifetime: 600 # 10 minutes
