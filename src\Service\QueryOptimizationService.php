<?php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class QueryOptimizationService
{
    private EntityManagerInterface $entityManager;
    private CacheInterface $documentCache;

    public function __construct(
        EntityManagerInterface $entityManager,
        CacheInterface $documentCache
    ) {
        $this->entityManager = $entityManager;
        $this->documentCache = $documentCache;
    }

    /**
     * Exécute une requête avec cache automatique
     */
    public function executeWithCache(string $cacheKey, callable $queryCallback, int $ttl = 300): mixed
    {
        return $this->documentCache->get($cacheKey, function (ItemInterface $item) use ($queryCallback, $ttl) {
            $item->expiresAfter($ttl);
            return $queryCallback();
        });
    }

    /**
     * Optimise les requêtes JSON pour les current_steps
     */
    public function findDocumentsByCurrentStepOptimized(string $step, int $limit = 100): array
    {
        $cacheKey = "documents_step_{$step}_{$limit}";
        
        return $this->executeWithCache($cacheKey, function () use ($step, $limit) {
            $conn = $this->entityManager->getConnection();
            
            // Requête optimisée avec index sur current_steps
            $sql = "
                SELECT d.id, d.reference, d.ref_rev, d.ref_title_fra, d.doc_type, 
                       d.material_type, d.proc_type, d.current_steps
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, CONCAT('$.\"', ?, '\"')) IS NOT NULL
                ORDER BY d.id DESC
                LIMIT ?
            ";
            
            $result = $conn->executeQuery($sql, [$step, $limit], [\PDO::PARAM_STR, \PDO::PARAM_INT]);
            return $result->fetchAllAssociative();
        }, 180); // Cache 3 minutes
    }

    /**
     * Compte les documents par étape avec cache optimisé
     */
    public function countDocumentsByStepOptimized(): array
    {
        return $this->executeWithCache('document_counts_by_step', function () {
            $conn = $this->entityManager->getConnection();
            
            $workflowSteps = [
                'Produit', 'Qual_Logistique', 'Logistique', 'Quality', 'Project', 'Metro', 'QProd',
                'Assembly', 'Machining', 'Molding', 'Methode_assemblage',
                'Achat_Rfq', 'Achat_F30', 'Achat_FIA', 'Achat_RoHs_REACH', 'Achat_Hts',
                'Planning', 'Indus', 'Core_Data', 'Prod_Data', 'methode_Labo', 'GID', 'Costing'
            ];
            
            $counts = [];
            
            foreach ($workflowSteps as $step) {
                if ($step === 'Qual_Logistique') {
                    // Cas spécial pour les étapes logistiques
                    $sql = "
                        SELECT COUNT(DISTINCT d.id) as count_docs
                        FROM document d
                        WHERE (
                            JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                            OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                        )
                        AND NOT (
                            EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                            AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                        )
                    ";
                    $count = $conn->executeQuery($sql)->fetchOne();
                    $counts['Qual_Logistique'] = (int)$count;
                } else {
                    $sql = "
                        SELECT COUNT(d.id) as count_docs
                        FROM document d
                        WHERE JSON_EXTRACT(d.current_steps, CONCAT('$.\"', ?, '\"')) IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 FROM visa v
                            WHERE v.released_drawing_id = d.id
                            AND v.name = CONCAT('visa_', ?)
                            AND v.status = 'valid'
                        )
                    ";
                    $count = $conn->executeQuery($sql, [$step, $step])->fetchOne();
                    $counts[$step] = (int)$count;
                }
            }
            
            return $counts;
        }, 120); // Cache 2 minutes
    }

    /**
     * Invalide tous les caches liés aux documents
     */
    public function invalidateDocumentCaches(): void
    {
        // Invalider les caches principaux
        $this->documentCache->delete('document_counts_by_step');
        
        // Invalider les caches par étape (pattern matching)
        $workflowSteps = [
            'Produit', 'Qual_Logistique', 'Quality', 'Project', 'Metro', 'QProd',
            'Assembly', 'Machining', 'Molding', 'Methode_assemblage',
            'Achat_Rfq', 'Achat_F30', 'Achat_FIA', 'Achat_RoHs_REACH', 'Achat_Hts',
            'Planning', 'Indus', 'Core_Data', 'Prod_Data', 'methode_Labo', 'GID', 'Costing'
        ];
        
        foreach ($workflowSteps as $step) {
            $this->documentCache->delete("documents_step_{$step}_100");
        }
    }

    /**
     * Préchauffe les caches les plus utilisés
     */
    public function warmupCaches(): void
    {
        // Préchauffer le cache des comptages
        $this->countDocumentsByStepOptimized();
        
        // Préchauffer les caches des étapes les plus utilisées
        $popularSteps = ['Produit', 'Quality', 'Assembly', 'Costing'];
        foreach ($popularSteps as $step) {
            $this->findDocumentsByCurrentStepOptimized($step);
        }
    }
}
