framework:
    cache:
        # Configuration spécifique pour la production
        prefix_seed: 'frcmintranet_prod'
        
        # Cache principal optimisé
        app: cache.adapter.filesystem
        
        # Pools de cache optimisés pour la production
        pools:
            # Cache ultra-rapide pour les données critiques (en mémoire)
            memory.cache:
                adapter: cache.adapter.array
                default_lifetime: 300
                
            # Cache pour les données de documents
            document.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 300
                
            # Cache pour les statistiques navbar
            navbar.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 120
                
            # Cache pour les données de workflow
            workflow.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 1800
                
            # Cache système Doctrine optimisé
            doctrine.system_cache_pool:
                adapter: cache.adapter.filesystem
                default_lifetime: 3600
                
            # Cache résultats Doctrine
            doctrine.result_cache_pool:
                adapter: cache.adapter.filesystem
                default_lifetime: 600
                
            # Cache pour les requêtes lourdes
            heavy_queries.cache:
                adapter: cache.adapter.filesystem
                default_lifetime: 900 # 15 minutes
