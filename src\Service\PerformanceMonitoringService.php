<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Symfony\Contracts\Cache\CacheInterface;

class PerformanceMonitoringService
{
    private LoggerInterface $logger;
    private CacheInterface $cache;
    private array $timers = [];

    public function __construct(LoggerInterface $logger, CacheInterface $cache)
    {
        $this->logger = $logger;
        $this->cache = $cache;
    }

    /**
     * Démarre un timer pour mesurer les performances
     */
    public function startTimer(string $name): void
    {
        $this->timers[$name] = microtime(true);
    }

    /**
     * Arrête un timer et log le résultat
     */
    public function stopTimer(string $name, array $context = []): float
    {
        if (!isset($this->timers[$name])) {
            $this->logger->warning("Timer '{$name}' n'a pas été démarré");
            return 0.0;
        }

        $duration = microtime(true) - $this->timers[$name];
        unset($this->timers[$name]);

        // Logger si la durée dépasse un seuil
        if ($duration > 1.0) { // Plus d'1 seconde
            $this->logger->warning("Performance lente détectée", [
                'timer' => $name,
                'duration' => $duration,
                'context' => $context
            ]);
        } elseif ($duration > 0.5) { // Plus de 500ms
            $this->logger->info("Performance à surveiller", [
                'timer' => $name,
                'duration' => $duration,
                'context' => $context
            ]);
        }

        return $duration;
    }

    /**
     * Mesure l'exécution d'une fonction
     */
    public function measure(string $name, callable $callback, array $context = []): mixed
    {
        $this->startTimer($name);
        try {
            $result = $callback();
            return $result;
        } finally {
            $this->stopTimer($name, $context);
        }
    }

    /**
     * Enregistre les métriques de performance
     */
    public function recordMetric(string $metric, float $value, array $tags = []): void
    {
        $timestamp = time();
        $key = "metric_{$metric}_{$timestamp}";
        
        $data = [
            'metric' => $metric,
            'value' => $value,
            'timestamp' => $timestamp,
            'tags' => $tags
        ];

        // Stocker en cache pour analyse ultérieure
        $this->cache->get($key, function() use ($data) {
            return $data;
        });

        // Logger les métriques importantes
        if ($value > $this->getThreshold($metric)) {
            $this->logger->warning("Métrique élevée détectée", $data);
        }
    }

    /**
     * Récupère les métriques de performance récentes
     */
    public function getRecentMetrics(string $metric, int $minutes = 60): array
    {
        // Cette méthode nécessiterait une implémentation plus complexe
        // avec un système de stockage des métriques
        return [];
    }

    /**
     * Analyse les performances et génère des recommandations
     */
    public function analyzePerformance(): array
    {
        $recommendations = [];

        // Analyser les temps de réponse moyens
        // Analyser l'utilisation du cache
        // Analyser les requêtes lentes
        
        return $recommendations;
    }

    /**
     * Obtient le seuil d'alerte pour une métrique
     */
    private function getThreshold(string $metric): float
    {
        $thresholds = [
            'database_query_time' => 1.0, // 1 seconde
            'page_load_time' => 2.0,      // 2 secondes
            'cache_miss_rate' => 0.3,     // 30%
            'memory_usage' => 128 * 1024 * 1024, // 128MB
        ];

        return $thresholds[$metric] ?? 1.0;
    }

    /**
     * Génère un rapport de performance
     */
    public function generatePerformanceReport(): array
    {
        return [
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'active_timers' => array_keys($this->timers),
        ];
    }
}
