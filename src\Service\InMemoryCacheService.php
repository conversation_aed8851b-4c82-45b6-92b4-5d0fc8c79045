<?php

namespace App\Service;

/**
 * Service de cache en mémoire simple pour les données critiques
 * Alternative à APCu quand celui-ci n'est pas disponible
 */
class InMemoryCacheService
{
    private static array $cache = [];
    private static array $expiry = [];

    /**
     * Stocke une valeur en cache avec expiration
     */
    public function set(string $key, mixed $value, int $ttl = 300): void
    {
        self::$cache[$key] = $value;
        self::$expiry[$key] = time() + $ttl;
    }

    /**
     * Récupère une valeur du cache
     */
    public function get(string $key, mixed $default = null): mixed
    {
        // Vérifier si la clé existe et n'est pas expirée
        if (!isset(self::$cache[$key]) || !isset(self::$expiry[$key])) {
            return $default;
        }

        if (time() > self::$expiry[$key]) {
            // Clé expirée, la supprimer
            unset(self::$cache[$key], self::$expiry[$key]);
            return $default;
        }

        return self::$cache[$key];
    }

    /**
     * Vérifie si une clé existe et n'est pas expirée
     */
    public function has(string $key): bool
    {
        if (!isset(self::$cache[$key]) || !isset(self::$expiry[$key])) {
            return false;
        }

        if (time() > self::$expiry[$key]) {
            unset(self::$cache[$key], self::$expiry[$key]);
            return false;
        }

        return true;
    }

    /**
     * Supprime une clé du cache
     */
    public function delete(string $key): void
    {
        unset(self::$cache[$key], self::$expiry[$key]);
    }

    /**
     * Vide tout le cache
     */
    public function clear(): void
    {
        self::$cache = [];
        self::$expiry = [];
    }

    /**
     * Récupère ou calcule une valeur avec cache
     */
    public function remember(string $key, callable $callback, int $ttl = 300): mixed
    {
        if ($this->has($key)) {
            return $this->get($key);
        }

        $value = $callback();
        $this->set($key, $value, $ttl);
        return $value;
    }

    /**
     * Nettoie les clés expirées
     */
    public function cleanup(): void
    {
        $now = time();
        foreach (self::$expiry as $key => $expiry) {
            if ($now > $expiry) {
                unset(self::$cache[$key], self::$expiry[$key]);
            }
        }
    }

    /**
     * Retourne les statistiques du cache
     */
    public function getStats(): array
    {
        $this->cleanup();
        
        return [
            'keys_count' => count(self::$cache),
            'memory_usage' => memory_get_usage(true),
            'cache_keys' => array_keys(self::$cache)
        ];
    }
}
