<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Welcome!{% endblock %}</title>
    <link rel="icon" href="{{ asset('icon.png') }}" type="image/png">
    <!-- Préchargement DNS pour améliorer les performances -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//unpkg.com">

    <!-- CSS critiques en premier (non-bloquant) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/css/bootstrap-select.min.css" integrity="sha512-g2SduJKxa4Lbn3GW+Q7rNz+pKP9AWMR++Ta8fgwsZRCUsawjPvF/BxSMkGS61VsR9yinGoEgrHPGPn2mrj8+4w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" />

    <!-- Scripts critiques -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Scripts non-critiques chargés de manière asynchrone -->
    <script async src="https://cdn.lordicon.com/lordicon.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/js/bootstrap-select.min.js" integrity="sha512-yrOmjPdp8qH8hgLfWpSFhC/+R9Cj9USL8uJxYIveJZGAiedxyIxwNw4RsLDlcjNlIRR4kkHaDHSmNHAkxFTmgg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js"></script>
    <script defer src="https://unpkg.com/@popperjs/core@2"></script>
    <script defer src="https://unpkg.com/tippy.js@6"></script>




</head>
{% block navbar %}
{% include '_partials/_nav.html.twig' %}
{% endblock %}

<style>
#modal-visa {
    border-radius: 10px;
    overflow: hidden;
}

.badge.bg-primary {
    background:  #0059B3!important;
}

#modal-visa thead th {
    background-color: #6c757d!important;
    color: white!important;
    border: none!important;
}

#modal-visa tbody td {
    text-align: center;
    vertical-align: middle;
    border: none!important;
}

.selected-row {
    background-color: #ECECEC!important;
}

#table tr td {
    font-size: 14px;
}

/* Styles pour la pagination */
.pagination-container {
    margin: 20px 0;
}

.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: #0059B3;
    border-color: #0059B3;
}

.pagination-container .page-link {
    color: #0059B3;
}

.pagination-container .page-link:hover {
    color: #003366;
    background-color: #e9ecef;
}

@media (max-width: 1600px) {
    #table tr td {
        font-size: 12px;
    }
}

.row-highlight>td {
    background-color:rgba(2, 116, 216, 0.47) !important;
    border: none!important;
    transition: all 0.5s ease;
}

td{
    transition: all 0.5s ease;
}

/* Styles pour l'indicateur de chargement global */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-text {
    margin-top: 10px;
    color: #0059B3;
    font-weight: bold;
}

</style>
<body>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        {% for message in app.flashes('success') %}
            Toast.fire({
                icon: "success",
                title: "{{ message }}"
            });
        {% endfor %}

        {% for message in app.flashes('danger') %}
            Toast.fire({
                icon: "error",
                title: "{{ message }}"
            });
        {% endfor %}

        {% for message in app.flashes('error') %}
            Toast.fire({
                icon: "error",
                title: "{{ message }}"
            });
        {% endfor %}


        $(document).on('input', '.UPPERINPUT', function(){
            var value = $(this).val();
            value = value.toUpperCase();
            $(this).val(value);
        });

    </script>

    <!-- Global Loading Indicator -->
    {# <div class="loading-overlay" id="globalLoadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <div class="loading-text">Chargement en cours...</div>
        </div>
    </div> #}

    {% block body %}{% endblock %}

    {% block javascripts %}
    {# <script>
        // Global AJAX loading indicator functions
        function showGlobalLoading() {
            $('#globalLoadingOverlay').css('display', 'flex');
        }

        function hideGlobalLoading() {
            $('#globalLoadingOverlay').css('display', 'none');
        }

        // Setup global AJAX event handlers
        $(document).ajaxStart(function() {
            showGlobalLoading();
        });

        $(document).ajaxStop(function() {
            hideGlobalLoading();
        });

        // For specific AJAX calls that shouldn't show the global loader,
        // use the option { global: false } in the $.ajax() call
    </script> #}

    {% endblock %}
</body>
</html>
