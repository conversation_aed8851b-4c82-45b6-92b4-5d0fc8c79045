<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:optimize-database',
    description: 'Optimise la base de données pour améliorer les performances'
)]
class OptimizeDatabaseCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Optimisation de la base de données');

        try {
            // 1. Créer des index pour optimiser les requêtes JSON
            $this->createJsonIndexes($io);

            // 2. Analyser et optimiser les tables
            $this->optimizeTables($io);

            // 3. Mettre à jour les statistiques
            $this->updateStatistics($io);

            $io->success('Optimisation de la base de données terminée avec succès !');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Erreur lors de l\'optimisation : ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function createJsonIndexes(SymfonyStyle $io): void
    {
        $io->section('Création des index JSON');

        // Index pour current_steps (requêtes les plus fréquentes)
        $indexes = [
            'idx_document_current_steps' => "CREATE INDEX idx_document_current_steps ON document ((JSON_KEYS(current_steps)))",
            'idx_document_state_timestamps' => "CREATE INDEX idx_document_state_timestamps ON document ((JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))",
            'idx_document_reference' => "CREATE INDEX idx_document_reference ON document (reference, ref_rev)",
            'idx_document_doc_type' => "CREATE INDEX idx_document_doc_type ON document (doc_type, material_type, proc_type)",
            'idx_visa_document_name_status' => "CREATE INDEX idx_visa_document_name_status ON visa (released_drawing_id, name, status)",
            'idx_visa_status_name' => "CREATE INDEX idx_visa_status_name ON visa (status, name)"
        ];

        foreach ($indexes as $indexName => $sql) {
            try {
                // Vérifier si l'index existe déjà
                $existingIndexes = $this->connection->executeQuery(
                    "SHOW INDEX FROM document WHERE Key_name = ?",
                    [$indexName]
                )->fetchAllAssociative();

                if (empty($existingIndexes)) {
                    $this->connection->executeStatement($sql);
                    $io->text("✓ Index créé : {$indexName}");
                } else {
                    $io->text("- Index existe déjà : {$indexName}");
                }
            } catch (\Exception $e) {
                $io->warning("Impossible de créer l'index {$indexName} : " . $e->getMessage());
            }
        }
    }

    private function optimizeTables(SymfonyStyle $io): void
    {
        $io->section('Optimisation des tables');

        $tables = ['document', 'visa', 'released_package', 'project', 'user', 'commentaire'];

        foreach ($tables as $table) {
            try {
                $this->connection->executeStatement("OPTIMIZE TABLE {$table}");
                $io->text("✓ Table optimisée : {$table}");
            } catch (\Exception $e) {
                $io->warning("Impossible d'optimiser la table {$table} : " . $e->getMessage());
            }
        }
    }

    private function updateStatistics(SymfonyStyle $io): void
    {
        $io->section('Mise à jour des statistiques');

        $tables = ['document', 'visa', 'released_package', 'project'];

        foreach ($tables as $table) {
            try {
                $this->connection->executeStatement("ANALYZE TABLE {$table}");
                $io->text("✓ Statistiques mises à jour : {$table}");
            } catch (\Exception $e) {
                $io->warning("Impossible de mettre à jour les statistiques de {$table} : " . $e->getMessage());
            }
        }

        // Afficher quelques statistiques utiles
        $this->showDatabaseStats($io);
    }

    private function showDatabaseStats(SymfonyStyle $io): void
    {
        $io->section('Statistiques de la base de données');

        try {
            // Nombre total de documents
            $totalDocs = $this->connection->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();
            $io->text("Documents total : " . number_format($totalDocs));

            // Nombre de documents avec current_steps
            $docsWithSteps = $this->connection->executeQuery(
                "SELECT COUNT(*) FROM document WHERE current_steps IS NOT NULL AND current_steps != '{}'"
            )->fetchOne();
            $io->text("Documents avec étapes : " . number_format($docsWithSteps));

            // Nombre de visas
            $totalVisas = $this->connection->executeQuery("SELECT COUNT(*) FROM visa")->fetchOne();
            $io->text("Visas total : " . number_format($totalVisas));

            // Taille de la base de données
            $dbSize = $this->connection->executeQuery(
                "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB' 
                 FROM information_schema.tables 
                 WHERE table_schema = DATABASE()"
            )->fetchOne();
            $io->text("Taille de la base : {$dbSize} MB");

        } catch (\Exception $e) {
            $io->warning("Impossible de récupérer les statistiques : " . $e->getMessage());
        }
    }
}
