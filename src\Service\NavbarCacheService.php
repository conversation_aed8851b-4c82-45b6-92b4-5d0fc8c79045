<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

class NavbarCacheService
{
    private DocumentRepository $documentRepository;
    private CacheInterface $navbarCache;

    public function __construct(
        DocumentRepository $documentRepository,
        CacheInterface $navbarCache
    ) {
        $this->documentRepository = $documentRepository;
        $this->navbarCache = $navbarCache;
    }

    /**
     * Récupère les badges de la navbar avec cache optimisé
     */
    public function getNavbarBadges(): array
    {
        return $this->navbarCache->get('navbar_badges', function (ItemInterface $item) {
            $item->expiresAfter(120); // Cache pendant 2 minutes
            
            // Utiliser la méthode optimisée du repository
            return $this->documentRepository->countDocumentsByWorkflowStep();
        });
    }

    /**
     * Récupère les statistiques de temps avec cache
     */
    public function getTimeStats(): array
    {
        return $this->navbarCache->get('time_stats', function (ItemInterface $item) {
            $item->expiresAfter(300); // Cache pendant 5 minutes
            
            return $this->documentRepository->getNavbarStatsOptimized();
        });
    }

    /**
     * Invalide le cache des badges (à appeler lors de modifications)
     */
    public function invalidateBadgesCache(): void
    {
        $this->navbarCache->delete('navbar_badges');
        $this->navbarCache->delete('time_stats');
    }

    /**
     * Préchauffe le cache des badges
     */
    public function warmupCache(): void
    {
        $this->getNavbarBadges();
        $this->getTimeStats();
    }
}
